"""
Airport Database Module

This module contains airport data including coordinates, runway information,
and utility functions for airport lookups.
"""

from typing import Dict, <PERSON>, Tu<PERSON>, Optional

# Airport database with detailed information
AIRPORTS = {
    "EDDF": {
        "name": "Frankfurt am Main Airport",
        "city": "Frankfurt",
        "country": "Germany",
        "latitude": 50.0333,
        "longitude": 8.5706,
        "elevation_ft": 364,
        "runways": [
            {"direction": "07C/25C", "length_ft": 13123, "surface": "Asphalt"},
            {"direction": "07L/25R", "length_ft": 13123, "surface": "Asphalt"},
            {"direction": "07R/25L", "length_ft": 13123, "surface": "Asphalt"},
            {"direction": "18", "length_ft": 13123, "surface": "Asphalt"}
        ]
    },
    "KJFK": {
        "name": "John F. Kennedy International Airport",
        "city": "New York",
        "country": "United States",
        "latitude": 40.6413,
        "longitude": -73.7781,
        "elevation_ft": 13,
        "runways": [
            {"direction": "04L/22R", "length_ft": 12079, "surface": "Asphalt"},
            {"direction": "04R/22L", "length_ft": 8400, "surface": "Asphalt"},
            {"direction": "13L/31R", "length_ft": 10000, "surface": "Asphalt"},
            {"direction": "13R/31L", "length_ft": 14511, "surface": "Asphalt"}
        ]
    },
    "EGLL": {
        "name": "Heathrow Airport",
        "city": "London",
        "country": "United Kingdom",
        "latitude": 51.4700,
        "longitude": -0.4543,
        "elevation_ft": 83,
        "runways": [
            {"direction": "09L/27R", "length_ft": 12799, "surface": "Asphalt"},
            {"direction": "09R/27L", "length_ft": 12008, "surface": "Asphalt"}
        ]
    },
    "EHAM": {
        "name": "Amsterdam Airport Schiphol",
        "city": "Amsterdam",
        "country": "Netherlands",
        "latitude": 52.3086,
        "longitude": 4.7639,
        "elevation_ft": -11,
        "runways": [
            {"direction": "18R/36L", "length_ft": 12467, "surface": "Asphalt"},
            {"direction": "06/24", "length_ft": 11483, "surface": "Asphalt"},
            {"direction": "09/27", "length_ft": 11483, "surface": "Asphalt"},
            {"direction": "18C/36C", "length_ft": 11483, "surface": "Asphalt"},
            {"direction": "18L/36R", "length_ft": 11483, "surface": "Asphalt"},
            {"direction": "04/22", "length_ft": 6791, "surface": "Asphalt"}
        ]
    },
    "LFPG": {
        "name": "Charles de Gaulle Airport",
        "city": "Paris",
        "country": "France",
        "latitude": 49.0097,
        "longitude": 2.5479,
        "elevation_ft": 392,
        "runways": [
            {"direction": "08L/26R", "length_ft": 13796, "surface": "Asphalt"},
            {"direction": "08R/26L", "length_ft": 13796, "surface": "Asphalt"},
            {"direction": "09L/27R", "length_ft": 13796, "surface": "Asphalt"},
            {"direction": "09R/27L", "length_ft": 13796, "surface": "Asphalt"}
        ]
    },
    "RJTT": {
        "name": "Tokyo Haneda Airport",
        "city": "Tokyo",
        "country": "Japan",
        "latitude": 35.5523,
        "longitude": 139.7798,
        "elevation_ft": 21,
        "runways": [
            {"direction": "04/22", "length_ft": 9843, "surface": "Asphalt"},
            {"direction": "16L/34R", "length_ft": 9843, "surface": "Asphalt"},
            {"direction": "16R/34L", "length_ft": 9843, "surface": "Asphalt"},
            {"direction": "05/23", "length_ft": 9843, "surface": "Asphalt"}
        ]
    },
    "YSSY": {
        "name": "Sydney Kingsford Smith Airport",
        "city": "Sydney",
        "country": "Australia",
        "latitude": -33.9399,
        "longitude": 151.1753,
        "elevation_ft": 21,
        "runways": [
            {"direction": "07/25", "length_ft": 8202, "surface": "Asphalt"},
            {"direction": "16L/34R", "length_ft": 12999, "surface": "Asphalt"},
            {"direction": "16R/34L", "length_ft": 8202, "surface": "Asphalt"}
        ]
    },
    "FAOR": {
        "name": "O. R. Tambo International Airport",
        "city": "Johannesburg",
        "country": "South Africa",
        "latitude": -26.1337,
        "longitude": 28.2420,
        "elevation_ft": 5558,
        "runways": [
            {"direction": "03L/21R", "length_ft": 14500, "surface": "Asphalt"},
            {"direction": "03R/21L", "length_ft": 11155, "surface": "Asphalt"}
        ]
    },
    "CYYZ": {
        "name": "Toronto Pearson International Airport",
        "city": "Toronto",
        "country": "Canada",
        "latitude": 43.6777,
        "longitude": -79.6248,
        "elevation_ft": 569,
        "runways": [
            {"direction": "05/23", "length_ft": 11000, "surface": "Asphalt"},
            {"direction": "06L/24R", "length_ft": 11000, "surface": "Asphalt"},
            {"direction": "06R/24L", "length_ft": 11000, "surface": "Asphalt"},
            {"direction": "15L/33R", "length_ft": 11000, "surface": "Asphalt"},
            {"direction": "15R/33L", "length_ft": 11000, "surface": "Asphalt"}
        ]
    },
    "OMDB": {
        "name": "Dubai International Airport",
        "city": "Dubai",
        "country": "United Arab Emirates",
        "latitude": 25.2532,
        "longitude": 55.3657,
        "elevation_ft": 62,
        "runways": [
            {"direction": "12L/30R", "length_ft": 13123, "surface": "Asphalt"},
            {"direction": "12R/30L", "length_ft": 13123, "surface": "Asphalt"}
        ]
    },
    "ZBAA": {
        "name": "Beijing Capital International Airport",
        "city": "Beijing",
        "country": "China",
        "latitude": 40.0801,
        "longitude": 116.5846,
        "elevation_ft": 115,
        "runways": [
            {"direction": "01/19", "length_ft": 12467, "surface": "Concrete"},
            {"direction": "18L/36R", "length_ft": 12467, "surface": "Concrete"},
            {"direction": "18R/36L", "length_ft": 12467, "surface": "Concrete"}
        ]
    },
    "LOWL": {
        "name": "Linz Airport",
        "city": "Linz",
        "country": "Austria",
        "latitude": 48.2333,
        "longitude": 14.1875,
        "elevation_ft": 980,
        "runways": [
            {"direction": "08/26", "length_ft": 9843, "surface": "Asphalt"}
        ]
    }
}


def get_airport_coordinates(icao_code: str) -> Tuple[float, float]:
    """
    Get the latitude and longitude coordinates for an airport.
    
    Args:
        icao_code: ICAO airport code (e.g., "EDDF")
        
    Returns:
        Tuple of (latitude, longitude) in degrees, or (0.0, 0.0) if not found
    """
    airport_data = AIRPORTS.get(icao_code.upper())
    if airport_data:
        return (airport_data["latitude"], airport_data["longitude"])
    return (0.0, 0.0)


def get_airport_names_dict() -> Dict[str, str]:
    """
    Get a dictionary mapping ICAO codes to airport names.
    
    Returns:
        Dictionary with ICAO codes as keys and airport names as values
    """
    return {icao: data["name"] for icao, data in AIRPORTS.items()}


def get_airport_by_icao(icao_code: str) -> Optional[Dict[str, Any]]:
    """
    Get the complete airport information for a given ICAO code.
    
    Args:
        icao_code: ICAO airport code (e.g., "EDDF")
        
    Returns:
        Dictionary with airport information, or None if not found
    """
    return AIRPORTS.get(icao_code.upper())
