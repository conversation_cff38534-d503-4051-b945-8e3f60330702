"""
Additional methods for the ArincFlightSimulator class.
This file contains the simulation control and data update methods.
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
from typing import Dict, Any


def add_simulation_methods(cls):
    """Add simulation methods to the ArincFlightSimulator class."""
    
    def start_simulation(self) -> None:
        """Start the flight simulation."""
        # Get input values
        start_airport_name = start_var.get()
        dest_airport_name = dest_var.get()
        flight_number = flight_var.get()
        daytime = daytime_var.get()

        # Validate airport selection
        if not start_airport_name or not dest_airport_name:
            messagebox.showerror("Error", "Please select both start and destination airports")
            return

        # Convert airport names to ICAO codes
        start_icao = name_to_icao.get(start_airport_name, "")
        dest_icao = name_to_icao.get(dest_airport_name, "")

        if not start_icao or not dest_icao:
            messagebox.showerror("Error", "Please select valid airports")
            return
        
        # Validate that destination is different from start
        if start_icao == dest_icao:
            messagebox.showerror("Error", "Destination airport cannot be the same as start airport")
            return
        
        # Initialize simulation
        self.time_since_takeoff = 0
        self.start_airport = start_icao
        self.dest_airport = dest_icao
        self.flight_number = flight_number
        self.city_pair = f"{start_icao[:3]}{dest_icao[:3]}"  # Create city pair from ICAO codes
        
        # Update UI
        self.status_var.set("Initializing...")
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"Initializing simulation for flight {flight_number}\n")
        self.output_text.insert(tk.END, f"Route: {start_airport_name} ({start_icao}) → {dest_airport_name} ({dest_icao})\n")
        self.output_text.insert(tk.END, f"Departure time: {daytime}\n\n")
        self.output_text.insert(tk.END, "Setting up ARINC labels...\n")
        self.output_text.see(tk.END)
        
        # Simulate ARINC board initialization (similar to LSY_Arinc_TestSetup)
        self.output_text.insert(tk.END, "Initializing ARINC board...\n")
        self.output_text.insert(tk.END, "Setting up channels...\n")
        self.output_text.insert(tk.END, "Configuring labels...\n")

        # Show PbaPro status in output
        if self.pbapro_initialized:
            self.output_text.insert(tk.END, f"PbaPro connected - {len(self.labels)} labels configured\n")
            self.output_text.insert(tk.END, "Data will be sent to PbaPro database\n\n")
        else:
            self.output_text.insert(tk.END, "PbaPro not available - simulation only mode\n")
            self.output_text.insert(tk.END, "Data will be displayed in UI only\n\n")

        self.output_text.see(tk.END)
        
        # Start simulation thread
        self.running = True
        self.flight_thread = threading.Thread(target=self.run_simulation)
        self.flight_thread.daemon = True
        self.flight_thread.start()
        
        # Update button states
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.config_button.config(state=tk.DISABLED)  # Disable config during simulation

    def stop_simulation(self) -> None:
        """Stop the flight simulation."""
        self.running = False
        if self.flight_thread:
            self.flight_thread.join(timeout=1.0)
        
        self.status_var.set("Stopped")
        self.output_text.insert(tk.END, "\nSimulation stopped\n")
        self.output_text.see(tk.END)
        
        # Update button states
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.config_button.config(state=tk.NORMAL)  # Re-enable config when stopped

    def run_simulation(self) -> None:
        """Main simulation loop."""
        from .flight_model import simulate_flight_data
        from ..arinc.utilities import encode_flight_info
        
        self.status_var.set("Running")
        
        while self.running:
            # Get flight data
            flight_data = simulate_flight_data(
                self.start_airport,
                self.dest_airport,
                self.time_since_takeoff,
                self.random_seed,
                self.movement_margin
            )
            
            # Update progress bar
            self.progress_var.set(flight_data["progress"])
            
            # Generate ARINC words for flight info
            arinc_words = encode_flight_info(self.flight_number, self.city_pair)
            
            # Generate ARINC words for flight parameters
            # This would simulate what LSY_Arinc_TestSetup does with its labels
            self.update_arinc_data(flight_data, arinc_words)

            # Update actual ARINC labels if PbaPro is available
            self.update_arinc_labels(flight_data)
            
            # Increment simulation time
            self.time_since_takeoff += int(self.update_interval * self.time_acceleration)
            
            # Check if flight is complete
            if flight_data["progress"] >= 1.0:
                self.running = False
                self.status_var.set("Completed")
                self.output_text.insert(tk.END, "\nFlight completed\n")
                self.output_text.see(tk.END)
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
                self.config_button.config(state=tk.NORMAL)  # Re-enable config when completed
                break
            
            time.sleep(self.update_interval)

    # Add methods to the class
    cls.start_simulation = start_simulation
    cls.stop_simulation = stop_simulation
    cls.run_simulation = run_simulation
    
    return cls
