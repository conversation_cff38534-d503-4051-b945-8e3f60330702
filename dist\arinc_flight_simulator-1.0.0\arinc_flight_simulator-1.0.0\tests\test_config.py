"""
Tests for the configuration management module.
"""

import pytest
import tempfile
import os
from arinc_flight_simulator.core.config import ConfigManager


class TestConfigManager:
    """Test cases for configuration management."""

    def test_config_creation(self):
        """Test configuration manager creation."""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as tmp:
            config_file = tmp.name
        
        try:
            # Remove the file so ConfigManager creates it
            os.unlink(config_file)
            
            config_manager = ConfigManager(config_file)
            
            # Check that file was created
            assert os.path.exists(config_file)
            
            # Check default values
            assert config_manager.get_int('GENERAL', 'time_acceleration') == 10
            assert config_manager.get_float('GENERAL', 'update_interval') == 1.0
            assert config_manager.get_value('GENERAL', 'pbapro_board_name') == 'ResourceList.A429-Board2'
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_config_validation(self):
        """Test configuration validation."""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as tmp:
            config_file = tmp.name
        
        try:
            config_manager = ConfigManager(config_file)
            
            # Test valid configuration
            errors = config_manager.validate_config()
            assert len(errors) == 0
            
            # Test invalid time acceleration
            config_manager.set_value('GENERAL', 'time_acceleration', '200')
            errors = config_manager.validate_config()
            assert len(errors) > 0
            assert any('Time acceleration' in error for error in errors)
            
            # Test invalid update interval
            config_manager.set_value('GENERAL', 'time_acceleration', '10')  # Fix previous error
            config_manager.set_value('GENERAL', 'update_interval', '20.0')
            errors = config_manager.validate_config()
            assert len(errors) > 0
            assert any('Update interval' in error for error in errors)
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_channel_config(self):
        """Test channel configuration retrieval."""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as tmp:
            config_file = tmp.name
        
        try:
            config_manager = ConfigManager(config_file)
            
            # Get channel configuration
            channels = config_manager.get_channel_config()
            
            # Should have enabled channels
            assert isinstance(channels, dict)
            assert len(channels) >= 2  # At least channels 1 and 2 should be enabled by default
            
            # Check channel speeds
            for channel_no, speed in channels.items():
                assert isinstance(channel_no, int)
                assert speed in ['12.5', '100', 'Low', 'High']
                
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_label_mapping(self):
        """Test label mapping configuration."""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as tmp:
            config_file = tmp.name
        
        try:
            config_manager = ConfigManager(config_file)

            # The new config manager creates default labels, so let's add some test labels
            config_manager.set_value('LABELS', 'Test-Label-1', '1,x')
            config_manager.set_value('LABELS', 'Test-Label-2', '2,310')
            config_manager.save_config()

            # Reload to get the saved labels
            config_manager.load_config()

            # Get label mapping
            mapping = config_manager.get_label_mapping()

            # Should have label mappings
            assert isinstance(mapping, dict)
            assert len(mapping) >= 2  # At least our test labels

            # Check our test labels
            assert 'test-label-1' in mapping  # Config keys are lowercase
            assert 'test-label-2' in mapping

            # Check label structure
            channel_no, label_string = mapping['test-label-1']
            assert isinstance(channel_no, int)
            assert 1 <= channel_no <= 4
            assert isinstance(label_string, str)
            assert label_string == 'x'

            channel_no, label_string = mapping['test-label-2']
            assert isinstance(channel_no, int)
            assert 1 <= channel_no <= 4
            assert isinstance(label_string, str)
            assert label_string == '310'
                
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_config_save_load(self):
        """Test configuration save and load."""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as tmp:
            config_file = tmp.name
        
        try:
            # Create and modify configuration
            config_manager = ConfigManager(config_file)
            config_manager.set_value('GENERAL', 'time_acceleration', '20')
            config_manager.set_value('GENERAL', 'random_seed', '456')
            
            # Save configuration
            assert config_manager.save_config() == True
            
            # Create new instance and verify values
            config_manager2 = ConfigManager(config_file)
            assert config_manager2.get_int('GENERAL', 'time_acceleration') == 20
            assert config_manager2.get_int('GENERAL', 'random_seed') == 456
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_boolean_values(self):
        """Test boolean configuration values."""
        with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as tmp:
            config_file = tmp.name
        
        try:
            config_manager = ConfigManager(config_file)
            
            # Test default boolean values
            assert config_manager.get_boolean('CHANNELS', 'channel_1_enabled') == True
            assert config_manager.get_boolean('CHANNELS', 'channel_3_enabled') == False
            
            # Test setting boolean values
            config_manager.set_value('CHANNELS', 'channel_3_enabled', 'True')
            assert config_manager.get_boolean('CHANNELS', 'channel_3_enabled') == True
            
            config_manager.set_value('CHANNELS', 'channel_1_enabled', 'False')
            assert config_manager.get_boolean('CHANNELS', 'channel_1_enabled') == False
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)
