"""
Flight Model Module

This module contains the flight simulation logic including flight phases,
navigation calculations, and realistic flight parameter generation.
"""

import math
import random
from typing import Dict, Any, Optional, Tuple

from ..data.airports import get_airport_coordinates


def haversine_nm(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate the great circle distance between two points on Earth in nautical miles.
    
    Args:
        lat1, lon1: Latitude and longitude of first point in degrees
        lat2, lon2: Latitude and longitude of second point in degrees
        
    Returns:
        Distance in nautical miles
    """
    R = 3440.065  # Earth radius in nautical miles
    phi1, phi2 = math.radians(lat1), math.radians(lat2)
    dphi = math.radians(lat2 - lat1)
    dlambda = math.radians(lon2 - lon1)

    a = math.sin(dphi / 2) ** 2 + math.cos(phi1) * math.cos(phi2) * math.sin(dlambda / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    return R * c


def calculate_initial_compass_bearing(pointA: Tuple[float, float], pointB: Tuple[float, float]) -> float:
    """
    Calculate the initial compass bearing from point A to point B.
    
    Args:
        pointA: (latitude, longitude) of starting point
        pointB: (latitude, longitude) of destination point
        
    Returns:
        Initial bearing in degrees (0-360)
    """
    lat1 = math.radians(pointA[0])
    lat2 = math.radians(pointB[0])
    diffLong = math.radians(pointB[1] - pointA[1])

    x = math.sin(diffLong) * math.cos(lat2)
    y = math.cos(lat1) * math.sin(lat2) - (
        math.sin(lat1) * math.cos(lat2) * math.cos(diffLong))

    initial_bearing = math.atan2(x, y)
    initial_bearing = math.degrees(initial_bearing)
    compass_bearing = (initial_bearing + 360) % 360
    return compass_bearing


def get_continuous_noise(base_amplitude: float, time_seconds: float, 
                        movement_margin: Optional[float] = None, 
                        random_seed: Optional[int] = None, 
                        seed_offset: int = 0) -> float:
    """
    Generate continuous noise using sine function with random offset.
    One revolution equals 5 seconds as requested.
    
    Args:
        base_amplitude: Base amplitude for the noise
        time_seconds: Current time in seconds
        movement_margin: Multiplier for noise amplitude (0.0 = no noise)
        random_seed: Seed for deterministic noise generation
        seed_offset: Offset for creating different noise channels
        
    Returns:
        Noise value between -base_amplitude*movement_margin and +base_amplitude*movement_margin
    """
    if movement_margin is None or movement_margin == 0.0:
        return 0.0

    # Use seed to generate a consistent random offset for this noise channel
    if random_seed is not None:
        # Create a deterministic offset based on seed and channel
        offset_seed = random_seed + seed_offset
        # Use simple hash-like function to generate offset
        offset = ((offset_seed * 9973) % 10000) / 10000.0 * 2 * math.pi
    else:
        offset = 0.0

    # One revolution = 5 seconds, so frequency = 1/5 = 0.2 Hz
    frequency = 0.2  # Hz
    angular_frequency = 2 * math.pi * frequency

    # Generate sine wave with random offset, scaled by movement_margin and base_amplitude
    noise = math.sin(angular_frequency * time_seconds + offset) * movement_margin * base_amplitude
    return noise


def simulate_flight_data(start_airport: str, destination_airport: str, 
                        time_since_takeoff_seconds: float, 
                        random_seed: Optional[int] = None, 
                        movement_margin: Optional[float] = None) -> Dict[str, Any]:
    """
    Simulate realistic flight data for a given flight route and time.
    
    Args:
        start_airport: ICAO code of departure airport
        destination_airport: ICAO code of destination airport
        time_since_takeoff_seconds: Time elapsed since takeoff in seconds
        random_seed: Seed for reproducible random generation
        movement_margin: Multiplier for data fluctuation (0.0 = no fluctuation)
        
    Returns:
        Dictionary containing flight parameters including position, speed, altitude, etc.
        
    Raises:
        ValueError: If airport coordinates cannot be found
    """
    if random_seed is not None:
        random.seed(time_since_takeoff_seconds + random_seed)

    start_lat, start_lon = get_airport_coordinates(start_airport)
    dest_lat, dest_lon = get_airport_coordinates(destination_airport)

    if start_lat == 0.0 and start_lon == 0.0:
        raise ValueError(f"Start airport '{start_airport}' not found.")
    if dest_lat == 0.0 and dest_lon == 0.0:
        raise ValueError(f"Destination airport '{destination_airport}' not found.") 

    # Flight settings
    cruise_altitude = 35000  # feet
    cruise_ground_speed = 480  # knots average

    # Compute flight distance and time
    distance_nm = haversine_nm(start_lat, start_lon, dest_lat, dest_lon)
    total_flight_time_seconds = (distance_nm / cruise_ground_speed) * 3600  # seconds

    progress = min(time_since_takeoff_seconds / total_flight_time_seconds, 1.0)

    # Flight Phases
    climb_end = 0.15
    descent_start = 0.85
    if progress < climb_end:
        phase = "Climb"
    elif progress < descent_start:
        phase = "Cruise"
    else:
        phase = "Descent"

    # Lat/Lon interpolation
    current_lat = start_lat + (dest_lat - start_lat) * progress
    current_lon = start_lon + (dest_lon - start_lon) * progress

    # Altitude calculation with realistic phase transitions
    if phase == "Climb":
        altitude = cruise_altitude * (progress / climb_end)
    elif phase == "Cruise":
        altitude = cruise_altitude
    else:
        altitude = cruise_altitude * (1 - (progress - descent_start) / (1 - descent_start))
    
    # Add continuous noise to altitude
    altitude += get_continuous_noise(50, time_since_takeoff_seconds, movement_margin, random_seed, seed_offset=1)

    # Ground speed calculation with phase-appropriate variations
    if phase == "Climb":
        ground_speed = cruise_ground_speed * (0.5 + 0.5 * (progress / climb_end))
    elif phase == "Cruise":
        ground_speed = cruise_ground_speed + get_continuous_noise(5, time_since_takeoff_seconds, movement_margin, random_seed, seed_offset=2)
    else:
        ground_speed = cruise_ground_speed * (1 - 0.3 * ((progress - descent_start) / (1 - descent_start)))
    
    # Add additional speed variation
    ground_speed += get_continuous_noise(5, time_since_takeoff_seconds, movement_margin, random_seed, seed_offset=3)

    # Indicated Air Speed (IAS) - typically lower than ground speed
    indicated_air_speed = ground_speed - (30 + get_continuous_noise(5, time_since_takeoff_seconds, movement_margin, random_seed, seed_offset=4))

    # Heading calculation with minor variations
    heading = calculate_initial_compass_bearing((start_lat, start_lon), (dest_lat, dest_lon))
    heading += get_continuous_noise(2, time_since_takeoff_seconds, movement_margin, random_seed, seed_offset=5)
    heading %= 360

    # Calculate remaining flight time
    remaining_time_seconds = max(total_flight_time_seconds - time_since_takeoff_seconds, 0)

    return {
        "Flight_Phase": phase,
        "Latitude_of_aircraft": current_lat,
        "Longitude_of_aircraft": current_lon,
        "Ground_Speed_knots": ground_speed,
        "Heading_degrees": heading,
        "Altitude_of_aircraft_ft": altitude,
        "Indicated_Air_Speed_knots": indicated_air_speed,
        "ETA_seconds": remaining_time_seconds,
        "Total_Flight_Time_seconds": total_flight_time_seconds,
        "Distance_nm": distance_nm,
        "progress": progress
    }
