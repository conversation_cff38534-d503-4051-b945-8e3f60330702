"""
ARINC Flight Simulator Package

A Python-based flight simulator that generates ARINC 429 data for aviation applications.
This package provides a graphical user interface for configuring flight parameters and 
displays real-time ARINC 429 encoded flight data.

Features:
- Interactive GUI with optimized layout
- Real-time flight simulation with progress tracking
- ARINC 429 encoding of flight parameters
- PbaPro integration support
- Multiple airport support
- Realistic flight phase simulation
"""

__version__ = "1.0.0"
__author__ = "ARINC Flight Simulator Team"

# Import main components for easier access
from .core.simulator import ArincFlightSimulator
from .core.flight_model import simulate_flight_data
from .arinc.utilities import encode_flight_info
from .core.config import ConfigManager, ConfigDialog
from .ui.interface import create_ui

__all__ = [
    "ArincFlightSimulator",
    "simulate_flight_data", 
    "encode_flight_info",
    "ConfigManager",
    "ConfigDialog",
    "create_ui",
]
