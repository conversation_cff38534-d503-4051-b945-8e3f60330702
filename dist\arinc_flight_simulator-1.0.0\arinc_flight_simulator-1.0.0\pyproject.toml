[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "arinc-flight-simulator"
version = "1.0.0"
description = "A Python-based flight simulator that generates ARINC 429 data for aviation applications"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "ARINC Flight Simulator Team"}
]
keywords = ["aviation", "arinc", "flight-simulator", "arinc-429"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "black",
    "flake8",
    "mypy",
]

[project.urls]
Homepage = "https://github.com/your-org/arinc-flight-simulator"
Documentation = "https://github.com/your-org/arinc-flight-simulator#readme"
Repository = "https://github.com/your-org/arinc-flight-simulator.git"
"Bug Tracker" = "https://github.com/your-org/arinc-flight-simulator/issues"

[project.scripts]
arinc-simulator = "arinc_flight_simulator.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"arinc_flight_simulator.config" = ["*.ini"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
