"""
Tests for ARINC 429 utilities module.
"""

import pytest
from arinc_flight_simulator.arinc.utilities import (
    encode_char,
    encode_char2,
    encode_char3,
    encode_flight_info
)


class TestArincUtilities:
    """Test cases for ARINC 429 encoding functions."""

    def test_encode_char(self):
        """Test single character encoding."""
        # Test normal ASCII characters
        assert encode_char('A') == ord('A')
        assert encode_char('Z') == ord('Z')
        assert encode_char('0') == ord('0')
        assert encode_char('9') == ord('9')
        assert encode_char(' ') == ord(' ')

        # Test that result is 7-bit
        result = encode_char('A')
        assert result <= 0x7F

        # Test unsupported characters (should return space)
        assert encode_char('\x01') == ord(' ')  # Control character
        assert encode_char('\x7F') == ord(' ')  # DEL character

    def test_encode_char2(self):
        """Test two-character encoding."""
        # Test basic encoding
        word = encode_char2('A', 'B')
        
        # Verify the word contains both characters in correct positions
        c1 = (word >> 2) & 0x7F
        c2 = (word >> 10) & 0x7F
        
        assert c1 == ord('A')
        assert c2 == ord('B')

        # Test with numbers
        word = encode_char2('1', '2')
        c1 = (word >> 2) & 0x7F
        c2 = (word >> 10) & 0x7F
        
        assert c1 == ord('1')
        assert c2 == ord('2')

    def test_encode_char3(self):
        """Test three-character encoding."""
        # Test basic encoding
        word = encode_char3('A', 'B', 'C')
        
        # Verify the word contains all characters in correct positions
        c1 = word & 0x7F
        c2 = (word >> 7) & 0x7F
        c3 = (word >> 14) & 0x7F
        
        assert c1 == ord('A')
        assert c2 == ord('B')
        assert c3 == ord('C')

        # Test with mixed characters
        word = encode_char3('X', '1', 'Z')
        c1 = word & 0x7F
        c2 = (word >> 7) & 0x7F
        c3 = (word >> 14) & 0x7F
        
        assert c1 == ord('X')
        assert c2 == ord('1')
        assert c3 == ord('Z')

    def test_encode_flight_info(self):
        """Test flight information encoding."""
        flight_number = "LH441"
        city_pair = "FRANYC"
        
        result = encode_flight_info(flight_number, city_pair)
        
        # Check that result is a dictionary
        assert isinstance(result, dict)
        
        # Check that we have the expected number of entries
        # Flight number: 6 chars / 2 = 3 words
        # City pair: 6 chars / 3 = 2 words
        # Total: 5 words
        assert len(result) == 5

        # Check that all values are integers (ARINC words)
        for key, value in result.items():
            assert isinstance(value, int)
            assert value >= 0

        # Check that keys contain expected patterns
        flight_keys = [k for k in result.keys() if "FlightNr" in k]
        city_keys = [k for k in result.keys() if "CityPair" in k]
        
        assert len(flight_keys) == 3  # 3 flight number words
        assert len(city_keys) == 2   # 2 city pair words

    def test_encode_flight_info_padding(self):
        """Test that short strings are properly padded."""
        # Test short flight number
        result = encode_flight_info("AB", "XYZ")
        
        # Should still produce 3 flight number words and 2 city pair words
        flight_keys = [k for k in result.keys() if "FlightNr" in k]
        city_keys = [k for k in result.keys() if "CityPair" in k]
        
        assert len(flight_keys) == 3
        assert len(city_keys) == 2

    def test_encode_flight_info_long_strings(self):
        """Test that long strings are truncated to 6 characters."""
        # Test long strings (should be truncated to 6 chars)
        result = encode_flight_info("VERYLONGFLIGHTNUMBER", "VERYLONGCITYPAIR")
        
        # Should still produce exactly 3 flight number words and 2 city pair words
        flight_keys = [k for k in result.keys() if "FlightNr" in k]
        city_keys = [k for k in result.keys() if "CityPair" in k]
        
        assert len(flight_keys) == 3
        assert len(city_keys) == 2

    def test_encode_flight_info_empty_strings(self):
        """Test encoding with empty strings."""
        result = encode_flight_info("", "")
        
        # Should still produce the expected number of words (padded with spaces)
        assert len(result) == 5
        
        # All words should be valid (non-negative integers)
        for value in result.values():
            assert isinstance(value, int)
            assert value >= 0

    def test_encode_flight_info_special_characters(self):
        """Test encoding with special characters."""
        # Test with special characters that should be converted to spaces
        result = encode_flight_info("AB\x01CD", "XY\x7FZ")
        
        # Should still work and produce valid words
        assert len(result) == 5
        
        for value in result.values():
            assert isinstance(value, int)
            assert value >= 0

    def test_deterministic_encoding(self):
        """Test that encoding is deterministic."""
        flight_number = "LH441"
        city_pair = "FRANYC"
        
        result1 = encode_flight_info(flight_number, city_pair)
        result2 = encode_flight_info(flight_number, city_pair)
        
        # Results should be identical
        assert result1 == result2

    def test_different_inputs_different_outputs(self):
        """Test that different inputs produce different outputs."""
        result1 = encode_flight_info("LH441", "FRANYC")
        result2 = encode_flight_info("BA123", "LONPAR")
        
        # Results should be different
        assert result1 != result2
